"use strict";function _classCallCheck(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function initializeCllOnAndroid(){var e=function(e){var n=e.split("#")[0].split("?"),i={},o=/^\w+\:\/\/\w+/.test(e);return n=o?2===n.length?n[1]:"":n[0],n.replace(/([^&=]+)=([^&]*)/g,function(e,n,o){n=decodeURIComponent(n),o=decodeURIComponent(o),i[n]=o}),i},n=e(location.href);window.bridge=function(){function e(){this._remote_callback={},this._local_handlers={}}function n(){this.request_id=(new Date).getTime(),this.getJsonString=function(){return JSON.stringify(this.data)}}function i(e,i,o,t){n.call(this),this.data=i||{},this.data.request_id=this.request_id,this.data.request_method=e,this.data.extra_callback=t,this.data.has_callback=o||!!t}function o(e,i){n.call(this),this.data={},this.data.request_id=this.request_id,this.data.callback_id=e,this.data.callback_data=i}function t(e){this.request_id=e.request_id,this.request_data=e.request_data,this.callback_id=e.callback_id}function a(e){t.call(this,e),this.request_method=e.request_method}function l(e){t.call(this,e),this.callback_method=e.callback_method}e.prototype={constructor:e,register_local_request_handler:function(e,n){this._local_handlers[e]=n},dispatch_remote_call_request:function(e){this._remote_callback[e.request_id]=e,JavaBridge.onReceiveCallRequest(e.getJsonString())},dispatch_remote_callback_request:function(e){JavaBridge.onReceiveCallbackRequest(e.getJsonString())},dispatch_local_call_request:function(e){var n,i=this._local_handlers[e.request_method]||window[e.request_method],t=e.callback_id;t&&(n=function(e){var n=new o(t,e);JavaBridge.onReceiveCallbackRequest(n.getJsonString())}),i&&setTimeout(function(){i(e.request_data,n)},0)},dispatch_local_callback_request:function(e){var n=this,i=e.callback_id,o=this._remote_callback[i];if(o){var t;t=e.callback_method?o.data[e.callback_method]:o.data.extra_callback,t?setTimeout(function(){t(e.request_data),delete n._remote_callback[i]},0):delete n._remote_callback[i]}}};var c=new e;return{register_local_request_handler:function(e,n){c.register_local_request_handler(e,n)},invoke_remote_call:function(e,n,o,t){var a=new i(e,n,!!o,t);c.dispatch_remote_call_request(a)},invoke_remote_callback:function(e,n){var i=new o(e,n);c.dispatch_remote_callback_request(i)},on_receive_request:function(e){"string"==typeof e&&(e=JSON.parse(e)),e.request_method?c.dispatch_local_call_request(new a(e)):c.dispatch_local_callback_request(new l(e))}}}();var i=window.cll||{},o={userLogIn:function(e){bridge.invoke_remote_call("userLogIn",e)},bindMobile:function(e){bridge.invoke_remote_call("bindMobile",e,!0)},getUserInfo:function(e){bridge.invoke_remote_call("getUserInfo",e,!0)},popShareMenu:function(e){bridge.invoke_remote_call("popShareMenu",e,!0)},chooseImage:function(e){bridge.invoke_remote_call("chooseImage",e,!0)},uploadImage:function(e){bridge.invoke_remote_call("uploadImage",e,!0)},openLineDetail:function(e){bridge.invoke_remote_call("openLineDetail",e,!0)},openTransferPage:function(e){bridge.invoke_remote_call("openTransferPage",e,!0)},getLocalInfo:function(e){bridge.invoke_remote_call("getLocalInfo",e,!0)},getLocation:function(e){bridge.invoke_remote_call("getLocation",e,!0)},openSystemSettingPage:function(e){bridge.invoke_remote_call("openSystemSettingPage",e,!0)},openSpecifiedURL:function(e){bridge.invoke_remote_call("openSpecifiedURL",e,!0)},openSpecifiedProject:function(e){n.vc<48?(e.jumpTo=e.jumpToProject,bridge.invoke_remote_call("urlJump",e,!0)):"48"===n.vc||"49"===n.vc?(e.jumpTo=e.jumpToProject,bridge.invoke_remote_call("openSpecifiedProject",e,!0)):bridge.invoke_remote_call("openSpecifiedProject",e,!0)},openSchema:function(e){bridge.invoke_remote_call("openSchema",e)},setNavTitle:function(e){n.vc<48?bridge.invoke_remote_call("setTitle",e):bridge.invoke_remote_call("setNavTitle",e)},setNavStatus:function(e){n.vc<48?bridge.invoke_remote_call("controlNav",e,!0):bridge.invoke_remote_call("setNavStatus",e,!0)},setNavBtnStatus:function(e){n.vc<48?bridge.invoke_remote_call("btnControl",e,!0):bridge.invoke_remote_call("setNavBtnStatus",e,!0)},closeWindow:function(){bridge.invoke_remote_call("closeWindow")},updateApp:function(e){bridge.invoke_remote_call("updateApp")},feedPostSubject:function(e){n.vc<48?bridge.invoke_remote_call("feedShare",e,!0):bridge.invoke_remote_call("feedPostSubject",e,!0)},checkWiFiStatus:function(e){bridge.invoke_remote_call("checkWiFiStatus",e,!0)},copyToClipboard:function(e){bridge.invoke_remote_call("copyToClipboard",e,!0)},getClipboardContent:function(e){bridge.invoke_remote_call("getClipboardContent",e,!0)},startUploadingTrace:function(e){bridge.invoke_remote_call("startUploadingTrace",e,!0)},stopUploadingTrace:function(e){bridge.invoke_remote_call("stopUploadingTrace",e,!0)},getUploadingTraceStatus:function(e){bridge.invoke_remote_call("getUploadingTraceStatus",e,!0)},subwayStationDetail:function(e){bridge.invoke_remote_call("subwayStationDetail",e,!0)},subwayTransfer:function(e){bridge.invoke_remote_call("subwayTransfer",e,!0)},openFeed:function(e){bridge.invoke_remote_call("openFeed",e,!0)},getCityInfo:function(e){bridge.invoke_remote_call("getCityInfo",e,!0)},getDeviceInfo:function(e){bridge.invoke_remote_call("getDeviceInfo",e,!0)},getAppVersion:function(e){bridge.invoke_remote_call("getAppVersion",e,!0)},getNetworkType:function(e){bridge.invoke_remote_call("getNetworkType",e,!0)},openUGC:function(e){bridge.invoke_remote_call("openUGC",e,!0)},preloadUGCImages:function(e){bridge.invoke_remote_call("preloadUGCImages",e,!0)},useUGCSkin:function(e){bridge.invoke_remote_call("useUGCSkin",e,!0)},DBControl:function(e){bridge.invoke_remote_call("DBControl",e,!0)},openFeedChat:function(e){bridge.invoke_remote_call("openFeedChat",e,!0)},checkSchemas:function(e){e.androidSchemas&&(e.schemas=e.androidSchemas),bridge.invoke_remote_call("checkSchemas",e,!0)},openPay:function(e){bridge.invoke_remote_call("openPay",e,!0)},energyOpenShareCard:function(e){bridge.invoke_remote_call("energyOpenShareCard",e,!0)},energyOpenUrlPages:function(e){bridge.invoke_remote_call("energyOpenUrlPages",e,!0)},energyOpenFiction:function(e){bridge.invoke_remote_call("energyOpenFiction",e,!0)},energyOpen:function(e){bridge.invoke_remote_call("energyOpen",e,!0)},energyPageWakeUpSuccessCallback:function(e){bridge.invoke_remote_call("energyPageWakeUpSuccessCallback",e,!0)},energyAndroidOpenGame:function(e){bridge.invoke_remote_call("energyAndroidOpenGame",e,!0)},setScreenBrightness:function(e){bridge.invoke_remote_call("setScreenBrightness",e,!0)},getScreenBrightness:function(e){bridge.invoke_remote_call("getScreenBrightness",e,!0)},setKeepScreenOn:function(e){bridge.invoke_remote_call("setKeepScreenOn",e,!0)},userLogOut:function(e){bridge.invoke_remote_call("userLogOut",e,!0)},alipayAuth:function(e){bridge.invoke_remote_call("alipayAuth",e,!0)},scanQRCode:function(e){bridge.invoke_remote_call("scanQRCode",e,!0)},tbkSDKOpenUrl:function(e){bridge.invoke_remote_call("tbkSDKOpenUrl",e,!0)},bindWeChat:function(e){bridge.invoke_remote_call("bindWeChat",e,!0)},openFeedList:function(e){bridge.invoke_remote_call("openFeedList",e,!0)},openFeedsDetail:function(e){bridge.invoke_remote_call("openFeedsDetail",e,!0)},openPage:function(e){bridge.invoke_remote_call("openPage",e,!1)}};for(var t in o)o.hasOwnProperty(t)&&(i[t]=o[t]);i.exceptionHandle=function(e){},i.wakeUp=function(){},i.energyPageWakeUp=function(){},i.onUserCaptureScreen=function(){},i.onShareMenuPoped=function(){var e=document.title,n=document.getElementById("hidzy")?document.getElementById("hidzy").value:"",i=document.location.href;return{qq:{title:e,desc:n,link:i},wxsession:{title:e,desc:n,link:i},wxtimeline:{title:e,desc:n,link:i},sina:{title:e,desc:n,link:i},qzone:{title:e,desc:n,link:i},success:function(e){},fail:function(e){},cancel:function(){}}},window.cll=i,window.sdkRealCll=function(e,n){"checkSchemas"===e&&n.androidSchemas&&(n.schemas=n.androidSchemas),bridge.invoke_remote_call(e,n,!0)},window.sdkRealOn=function(e,n){bridge.register_local_request_handler(e,function(e,i){n(e,i)})},bridge.register_local_request_handler("__postShare",function(e){"48"===n.vc||"49"===n.vc?bridge.invoke_remote_call("onShareMenuPoped",i.onShareMenuPoped(),!0):bridge.invoke_remote_call("postShare",i.onShareMenuPoped(),!0)})}function initializeCllOnIos(){function e(e){if(i.vc<10180)window.WebViewJavascriptBridge?e(WebViewJavascriptBridge):document.addEventListener("WebViewJavascriptBridgeReady",function(){e(WebViewJavascriptBridge)},!1);else{if(window.WebViewJavascriptBridge)return e(WebViewJavascriptBridge);if(window.WVJBCallbacks)return window.WVJBCallbacks.push(e);window.WVJBCallbacks=[e];var n=document.createElement("iframe");n.style.display="none",n.src="wvjbscheme://__BRIDGE_LOADED__",document.documentElement.appendChild(n),setTimeout(function(){document.documentElement.removeChild(n)},0)}}var n=function(e){var n=e.split("#")[0].split("?"),i={},o=/^\w+\:\/\/\w+/.test(e);return n=o?2===n.length?n[1]:"":n[0],n.replace(/([^&=]+)=([^&]*)/g,function(e,n,o){n=decodeURIComponent(n),o=decodeURIComponent(o),i[n]=o}),i},i=n(location.href);i.vc<10180&&createBrigeForIOS(),e(function(e){function n(n){e.callHandler("userLogIn",{successUrl:n.successUrl,failUrl:n.failUrl},function(e){e.success?(e.success.boundType=JSON.parse(e.success.boundType),n.success(e.success)):e.fail?n.fail(e.fail):n.cancel(e.cancel)})}function i(n){e.callHandler("getUserInfo","",function(e){e.success?(e.success.boundType=JSON.parse(e.success.boundType),n.success(e.success)):n.fail(e.fail)})}function o(e){window.sdkRealCll("popShareMenu",e)}function t(e){window.sdkRealCll("chooseImage",e)}function a(e){window.sdkRealCll("uploadImage",e)}function l(e){window.sdkRealCll("openLineDetail",e)}function c(e){window.sdkRealCll("getLocalInfo",e)}function r(e){window.sdkRealCll("getLocation",e)}function s(e){window.sdkRealCll("openSystemSettingPage",e)}function d(e){window.sdkRealCll("openSpecifiedProject",e)}function u(e){window.sdkRealCll("openSpecifiedURL",e)}function f(e){window.sdkRealCll("openSchema",e)}function g(e){window.sdkRealCll("setNavTitle",e)}function p(e){window.sdkRealCll("setNavStatus",e)}function _(e){window.sdkRealCll("setNavBtnStatus",e)}function k(e){window.sdkRealCll("closeWindow",e)}function w(e){window.sdkRealCll("updateApp",e)}function h(e){window.sdkRealCll("checkWiFiStatus",e)}function v(e){window.sdkRealCll("copyToClipboard",e)}function m(e){window.sdkRealCll("getClipboardContent",e)}function b(e){window.sdkRealCll("subwayStationDetail",e)}function C(e){window.sdkRealCll("subwayTransfer",e)}function S(e){window.sdkRealCll("openFeed",e)}function y(e){window.sdkRealCll("preloadUGCImages",e)}function R(e){window.sdkRealCll("useUGCSkin",e)}function B(n){e.callHandler("DBControl",n,function(e){"get"===n.method?e.success?n.success(e.success):n.fail(e.fail):e.success?n.success():n.fail(e.fail)})}function U(e){window.sdkRealCll("openFeedChat",e)}function O(n){n.iosSchemas&&(n.schemas=n.iosSchemas),e.callHandler("checkSchemas",n,function(e){e.success?(e.success=e.success.status&&e.success.status.map(function(e){return e?1:0}),n.success(e.success)):n.fail(e.fail)})}function P(e){window.sdkRealCll("openPay",e)}function I(e){window.sdkRealCll("bindMobile",e)}function q(e){window.sdkRealCll("energyOpen",e)}function T(){e.callHandler("energyOpenFiction","",function(){})}function W(){e.callHandler("energyPageWakeUpSuccessCallback","",function(){})}function L(e){window.sdkRealCll("setScreenBrightness",e)}function J(e){window.sdkRealCll("getScreenBrightness",e)}function F(e){window.sdkRealCll("setKeepScreenOn",e)}function H(e){window.sdkRealCll("userLogOut",e)}function E(e){window.sdkRealCll("alipayAuth",e)}function j(e){window.sdkRealCll("scanQRCode",e)}function A(e){window.sdkRealCll("tbkSDKOpenUrl",e)}function D(e){window.sdkRealCll("bindWeChat",e)}function N(e){window.sdkRealCll("openPage",e)}function M(n){e.callHandler("openFeedList",n,function(e){e.success?n.success(e.success):n.fail(e.fail)})}function V(n){e.callHandler("openFeedsDetail",n,function(e){e.success?n.success(e.success):n.fail(e.fail)})}function G(e){window.sdkRealCll("energyOpenUrlPages",e)}function x(e){}function K(){}function Q(){}function z(){}var X=void 0,Y=void 0,Z=void 0;e.registerHandler("onShareMenuPopedSource",function(e,n){var i=window.cll&&cll.onShareMenuPoped&&cll.onShareMenuPoped();X=i.success||Function(),Y=i.fail||Function(),Z=i.cancel||Function(),delete i.success,delete i.fail,delete i.cancel,n(i)}),e.registerHandler("onShareMenuPopedSuccess",function(e,n){X(e)}),e.registerHandler("onShareMenuPopedFail",function(e,n){Y(e)}),e.registerHandler("onShareMenuPopedCancel",function(e,n){Z(e)}),window.sdkRealCll=function(n,i){"checkSchemas"===n&&i.iosSchemas&&(i.schemas=i.iosSchemas),e.callHandler(n,i,function(e){e.success?("userLogin"!==n&&"getUserInfo"!==n||(e.success.boundType=JSON.parse(e.success.boundType)),i.success(e.success)):e.fail?i.fail(e.fail):i.cancel(e.cancel)})},window.sdkRealOn=function(n,i){e.registerHandler(n,function(e,n){i(e,n)})},window.cll={userLogIn:n,getUserInfo:i,popShareMenu:o,chooseImage:t,uploadImage:a,openLineDetail:l,getLocalInfo:c,getLocation:r,openSystemSettingPage:s,openSpecifiedProject:d,openSpecifiedURL:u,openSchema:f,setNavTitle:g,setNavStatus:p,setNavBtnStatus:_,closeWindow:k,updateApp:w,checkWiFiStatus:h,copyToClipboard:v,getClipboardContent:m,exceptionHandle:x,subwayStationDetail:b,subwayTransfer:C,openFeed:S,preloadUGCImages:y,useUGCSkin:R,DBControl:B,openFeedChat:U,checkSchemas:O,openPay:P,bindMobile:I,wakeUp:K,energyOpen:q,energyPageWakeUp:Q,energyOpenFiction:T,energyPageWakeUpSuccessCallback:W,onUserCaptureScreen:z,setScreenBrightness:L,getScreenBrightness:J,setKeepScreenOn:F,userLogOut:H,alipayAuth:E,scanQRCode:j,tbkSDKOpenUrl:A,bindWeChat:D,openFeedList:M,openFeedsDetail:V,energyOpenUrlPages:G,openPage:N},e.registerHandler("exceptionHandle",function(e,n){window.cll.exceptionHandle(e)}),e.registerHandler("wakeUp",function(e,n){window.cll.wakeUp()}),e.registerHandler("energyPageWakeUp",function(e,n){window.cll.energyPageWakeUp()}),e.registerHandler("onUserCaptureScreen",function(e,n){window.cll.onUserCaptureScreen()})})}function initCllHarmonySdk(){var e=0,n={success:"_jsBridgeSuccess",fail:"_jsBridgeFail",cancel:"_jsBridgeCancel"};if(window.sdkRealCll=function(i,o){if(o=o||{},"object"!==("undefined"==typeof o?"undefined":_typeof(o)))throw Error("参数必须为对象类型");var t={},a={};for(var l in o)"success"===l||"fail"===l||"cancel"===l?"function"==typeof o[l]&&(a[l]=o[l]):"function"!=typeof o[l]&&(t[l]=o[l]);for(var l in a){var c="jscall"+e++;window[c]=a[l],t[n[l]]=c}try{var r=JSON.stringify(t);window.cll_js_bridge&&window.cll_js_bridge.call(i,r)}catch(s){console.error(s)}},window.cll={},window.cll_js_bridge&&window.cll_js_bridge.getMethods){var i=window.cll_js_bridge.getMethods().split(",");i.forEach(function(e){Object.defineProperty(window.cll,e,{get:function(){return function(n){window.sdkRealCll(e,n)}}})})}}var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_createClass=function(){function e(e,n){for(var i=0;i<n.length;i++){var o=n[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(n,i,o){return i&&e(n.prototype,i),o&&e(n,o),n}}(),createBrigeForIOS=function(){function e(e){s=e.createElement("iframe"),s.style.display="none",s.src=g+"://"+p,e.documentElement.appendChild(s)}function n(e){if(WebViewJavascriptBridge._messageHandler)throw new Error("WebViewJavascriptBridge.init called twice");WebViewJavascriptBridge._messageHandler=e;var n=u;u=null;for(var i=0;i<n.length;i++)c(n[i])}function i(e,n){a({data:e},n)}function o(e,n){f[e]=n}function t(e,n,i){a({handlerName:e,data:n},i)}function a(e,n){if(n){var i="cb_"+k++ +"_"+(new Date).getTime();_[i]=n,e.callbackId=i}d.push(e),s.src=g+"://"+p}function l(){var e=JSON.stringify(d);return d=[],e}function c(e){setTimeout(function(){var n,i=JSON.parse(e);if(i.responseId){if(n=_[i.responseId],!n)return;n(i.responseData),delete _[i.responseId]}else{if(i.callbackId){var o=i.callbackId;n=function(e){a({responseId:o,responseData:e})}}var t=WebViewJavascriptBridge._messageHandler;if(i.handlerName&&(t=f[i.handlerName]),"undefined"==typeof t)return void n("undefined");try{t(i.data,n)}catch(l){"undefined"!=typeof console&&console.log("WebViewJavascriptBridge: WARNING: javascript handler threw.",i,l)}}})}function r(e){u?u.push(e):c(e)}if(!window.WebViewJavascriptBridge){var s,d=[],u=[],f={},g="wvjbscheme",p="__WVJB_QUEUE_MESSAGE__",_={},k=1;window.WebViewJavascriptBridge={init:n,send:i,registerHandler:o,callHandler:t,_fetchQueue:l,_handleMessageFromObjC:r};var w=document;e(w);var h=w.createEvent("Events");h.initEvent("WebViewJavascriptBridgeReady"),h.bridge=WebViewJavascriptBridge,w.dispatchEvent(h),n()}},CQAd=function(){function e(n){_classCallCheck(this,e),this.adPosition=n,this.onCloseCallBack=null,this.onLoadCallBack=null,this.onErrorCallBack=null,this.onShowCallBack=null,this.onClickCallBack=null,this.onRewardCallBack=null}return _createClass(e,[{key:"load",value:function(){window.sdkCall("adEvent",{event:"loadAd",adPosition:this.adPosition})}},{key:"show",value:function(){window.sdkCall("adEvent",{event:"showAd",adPosition:this.adPosition})}},{key:"onClose",value:function(e){this.onCloseCallBack=e}},{key:"onLoad",value:function(e){this.onLoadCallBack=e}},{key:"onError",value:function(e){this.onErrorCallBack=e}},{key:"onShow",value:function(e){this.onShowCallBack=e}},{key:"onClick",value:function(e){this.onClickCallBack=e}},{key:"onReward",value:function(e){this.onRewardCallBack=e}}]),e}(),cq={ads:{},createCqAd:function(e){var n=e.adPosition,i=new CQAd(n);return this.ads[n]=i,window.sdkCall("adEvent",{event:"createAd",adPosition:n}),i},getAdInstance:function(e){return this.ads[e]},resourceLoaded:function(){window.sdkCall("adEvent",{event:"resourceLoaded"})}};var agent=navigator.userAgent,isIos=/iP(ad|hone|od)/.test(agent),isAndroid=/Android/i.test(agent),isChelaile=/Chelaile/i.test(agent),isOpenHarmony=/OpenHarmony/i.test(agent);window.sdkCall=function(e,n){if(window.sdkRealCll||window.cll)window.sdkRealCll(e,n);else var i=setInterval(function(){(window.sdkRealCll||window.cll)&&(clearInterval(i),window.sdkRealCll(e,n))},50)},window.sdkOn=function(e,n){if(window.sdkRealCll)window.sdkRealOn(e,n);else var i=setInterval(function(){window.sdkRealOn&&(clearInterval(i),window.sdkRealOn(e,n))},50)},isAndroid&&initializeCllOnAndroid(),isIos&&initializeCllOnIos(),isOpenHarmony&&initCllHarmonySdk();window.sdkOn("adEvent",function(e){var n=cq.getAdInstance(e.adPosition);if(n)switch(e.event){case"loadSuccess":n.onLoadCallBack&&n.onLoadCallBack();break;case"loadFailed":n.onErrorCallBack&&n.onErrorCallBack({"errorCode":e.errorCode,"errorMsg":e.errorMsg});break;case"showSuccess":n.onShowCallBack&&n.onShowCallBack();break;case"didClick":n.onClickCallBack&&n.onClickCallBack();break;case"didReward":n.onRewardCallBack&&n.onRewardCallBack();break;case"didClosed":n.onCloseCallBack&&n.onCloseCallBack()}}),window.cq=cq;
