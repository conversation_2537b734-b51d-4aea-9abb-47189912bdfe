<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <link rel="shortcut icon" href="/favicon.ico" />
    <title>
        <?=htmlspecialchars($game->name)?>
    </title>
    <link rel="stylesheet" href="/css/webview-close.css?v=1.0.2" />
</head>

<body>
    <?php if ($needSdk): ?>
    <script src="/sdk/ad-sdk.umd.min.js?v=1.0.4"></script>
    <?php endif; ?>

    <script>
        const needSdk = <?= $needSdk ? 'true' : 'false' ?>;

        // 在顶层窗口初始化 SDK
        async function initSDK() {
            try {
                await window.AdSDK.initInTopWindow({
                    appid: '<?=htmlspecialchars($game->code)?>',
                    channel: '<?=htmlspecialchars($channel->code)?>',
                    debug: true
                });
                console.log('SDK 初始化成功');
                // SDK 初始化成功后加载游戏iframe
                loadGameFrame();
            } catch (error) {
                console.error('SDK 初始化失败:', error);
            }
        }

        // 加载游戏iframe
        function loadGameFrame() {
            const gameFrame = document.getElementById('gameFrame');
            if (gameFrame) {
                gameFrame.src = '<?=htmlspecialchars($url)?>';
                gameFrame.style.display = 'block';
            }
        }

        // 页面加载后初始化
        window.addEventListener('load', () => {
            if (needSdk) {
                initSDK();
            } else {
                loadGameFrame();
            }
        });
    </script>

    <?php if ($needSdk): ?>
    <!-- 关闭按钮 -->
    <div class="webview-close-container">
        <div class="webview-dots">
            <div class="webview-dot"></div>
            <div class="webview-dot"></div>
            <div class="webview-dot"></div>
        </div>
        <div class="webview-separator"></div>
        <button class="webview-close-btn" id="webviewCloseBtn" title="关闭" aria-label="关闭页面"></button>
    </div>

    <!-- 确认对话框 -->
    <div class="confirm-dialog" id="confirmDialog">
        <div class="confirm-dialog-content">
            <div class="confirm-dialog-title">确认关闭</div>
            <div class="confirm-dialog-message">确定要关闭当前页面吗？</div>
            <div class="confirm-dialog-buttons">
                <button class="confirm-dialog-btn confirm-dialog-btn-cancel" id="cancelBtn">取消</button>
                <button class="confirm-dialog-btn confirm-dialog-btn-confirm" id="confirmBtn">确定</button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- 游戏iframe -->
    <iframe src="" frameborder="0" id="gameFrame" style="display: none;"></iframe>

    <?php
    // 检查是否存在渠道特定的 JS 文件
    $channelJsFile = "/js/{$channel->code}.js";
    $channelJsPath = public_path() . $channelJsFile;

    if (file_exists($channelJsPath)): ?>
    <script type="text/javascript" src="<?=htmlspecialchars($channelJsFile)?>"></script>
    <?php elseif ($needSdk): ?>
    <script type="text/javascript" src="/js/webview-close.js"></script>
    <?php endif; ?>
</body>

</html>