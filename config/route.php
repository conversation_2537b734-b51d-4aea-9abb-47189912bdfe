<?php

declare(strict_types=1);

use Webman\Route;

/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 *
 * @see      http://www.workerman.net/
 *
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */
Route::get('/channel/{channel: [a-zA-Z0-9_]+}/game/{game:[a-zA-Z0-9_]+}', 'app\controller\GameController@show');

// SDK心跳API路由
Route::group('/api/heartbeat', function () {
    Route::post('/heartbeat', 'app\api\controller\HeartbeatController@heartbeat');
});

// 玩家数据备份API路由
Route::group('/api/player-data', function () {
    Route::post('/backup', 'app\api\controller\PlayerDataController@backup');
    Route::get('/retrieve', 'app\api\controller\PlayerDataController@retrieve');
    Route::get('/retrieve/{backup_key}', 'app\api\controller\PlayerDataController@retrieve');
    Route::get('/stats', 'app\api\controller\PlayerDataController@stats');
});
